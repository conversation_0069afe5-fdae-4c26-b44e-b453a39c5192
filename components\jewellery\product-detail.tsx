"use client";
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { poppins } from '@/src/common/helper';
import { PRODUCT_LIST, JEWELLERY_TYPE } from '@/src/libs/constants';
import { IoHeartOutline, IoHeart } from 'react-icons/io5';
import { PiHandbag } from 'react-icons/pi';
import { IoIosArrowBack, IoIosArrowForward } from 'react-icons/io';
import { Select, Button } from 'antd';
import Navbar from './navbar';
import Footer from '../home/<USER>';

// Product interface based on the existing PRODUCT_LIST structure
interface Product {
    value: string;
    image: string;
    type: string;
    skuId?: string;
    price?: string;
    isInCart?: boolean;
    name?: string;
    description?: string;
    material?: string;
    color?: string;
    size?: string;
    diamondWeight?: string;
    certification?: string;
}

// Extended product data for detailed view
const EXTENDED_PRODUCT_DATA: Record<string, Partial<Product>> = {
    'P1': {
        name: 'Phoenix Diamond Wing Open Ring',
        description: 'Diamond open wing ring in 18K recycled gold handcrafted with Phoenix lab grown diamonds.',
        material: '18K Gold',
        color: 'Yellow',
        size: '6',
        diamondWeight: '0.25 ct',
        certification: 'Yes'
    },
    'P2': {
        name: 'Elegant Diamond Necklace',
        description: 'Stunning diamond necklace with premium craftsmanship.',
        material: '18K Gold',
        color: 'White',
        diamondWeight: '1.5 ct',
        certification: 'Yes'
    },
    'P3': {
        name: 'Classic Diamond Earrings',
        description: 'Timeless diamond earrings with exceptional brilliance.',
        material: '14K Gold',
        color: 'White',
        diamondWeight: '0.75 ct',
        certification: 'Yes'
    },
    'P4': {
        name: 'Luxury Diamond Bracelet',
        description: 'Exquisite diamond bracelet with premium craftsmanship.',
        material: '18K Gold',
        color: 'Yellow',
        diamondWeight: '2.0 ct',
        certification: 'Yes'
    },
    // Add more products as needed
};

const ProductDetail = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const productId = searchParams.get('id') || 'P1';

    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [selectedSize, setSelectedSize] = useState('6');
    const [selectedMaterial, setSelectedMaterial] = useState('18K');
    const [selectedColor, setSelectedColor] = useState('Yellow');
    const [diamondWeight, setDiamondWeight] = useState('0.25 ct');
    const [isWishlisted, setIsWishlisted] = useState(false);
    const [quantity, setQuantity] = useState(1);

    // Find the current product
    const baseProduct = PRODUCT_LIST.find(p => p.value === productId) || PRODUCT_LIST[0];
    const extendedData = EXTENDED_PRODUCT_DATA[productId] || {};
    const product = { ...baseProduct, ...extendedData };

    // Sample images for gallery (in real app, this would come from product data)
    const productImages = [
        product.image,
        product.image, // Duplicate for demo
        product.image,
        product.image
    ];

    // Related products (same type, different products)
    const relatedProducts = PRODUCT_LIST.filter(p =>
        p.type === product.type && p.value !== product.value
    ).slice(0, 4);

    const handlePrevImage = () => {
        setCurrentImageIndex(prev =>
            prev === 0 ? productImages.length - 1 : prev - 1
        );
    };

    const handleNextImage = () => {
        setCurrentImageIndex(prev =>
            prev === productImages.length - 1 ? 0 : prev + 1
        );
    };

    const handleAddToCart = () => {
        // Add to cart logic
        console.log('Added to cart:', { productId, quantity, selectedSize, selectedMaterial, selectedColor });
    };

    const handleWishlistToggle = () => {
        setIsWishlisted(!isWishlisted);
    };

    const handleCustomDesign = () => {
        // Navigate to custom design page
        console.log('Custom design requested');
    };

    return (
        <div className={`w-full min-h-screen bg-white ${poppins.className}`}>
            {/* Navbar */}
            <div className="relative">
                <Navbar />
            </div>

            {/* Breadcrumb */}
            <div className="px-8 py-4 text-sm text-gray-600">
                <span className="cursor-pointer hover:text-black" onClick={() => router.push('/')}>
                    Home
                </span>
                <span className="mx-2">/</span>
                <span className="cursor-pointer hover:text-black" onClick={() => router.push('/jewellery')}>
                    Jewellery
                </span>
                <span className="mx-2">/</span>
                <span className="text-black">
                    {JEWELLERY_TYPE.find(t => t.value === product.type)?.label || product.type}
                </span>
            </div>

            {/* Main Product Section */}
            <div className="px-8 py-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    {/* Left: Product Images */}
                    <div className="space-y-4">
                        {/* Main Image */}
                        <div className="relative aspect-square bg-gray-50 rounded-lg overflow-hidden">
                            <Image
                                src={productImages[currentImageIndex]}
                                alt={product.name || product.type}
                                fill
                                className="object-contain"
                                priority
                            />

                            {/* Navigation Arrows */}
                            <button
                                onClick={handlePrevImage}
                                className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all"
                            >
                                <IoIosArrowBack className="w-5 h-5" />
                            </button>
                            <button
                                onClick={handleNextImage}
                                className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all"
                            >
                                <IoIosArrowForward className="w-5 h-5" />
                            </button>

                            {/* Wishlist Icon */}
                            <button
                                onClick={handleWishlistToggle}
                                className="absolute top-4 right-4 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all"
                            >
                                {isWishlisted ? (
                                    <IoHeart className="w-6 h-6 text-red-500" />
                                ) : (
                                    <IoHeartOutline className="w-6 h-6 text-gray-700" />
                                )}
                            </button>
                        </div>

                        {/* Thumbnail Images */}
                        <div className="flex space-x-2">
                            {productImages.map((img, index) => (
                                <button
                                    key={index}
                                    onClick={() => setCurrentImageIndex(index)}
                                    className={`relative w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${
                                        currentImageIndex === index
                                            ? 'border-black'
                                            : 'border-gray-200 hover:border-gray-400'
                                    }`}
                                >
                                    <Image
                                        src={img}
                                        alt={`Product view ${index + 1}`}
                                        fill
                                        className="object-contain"
                                    />
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Right: Product Details */}
                    <div className="space-y-6">
                        {/* Product Title and Price */}
                        <div className="border-b border-gray-200 pb-6">
                            <h1 className="text-3xl font-medium text-black mb-2">
                                {product.name || `${JEWELLERY_TYPE.find(t => t.value === product.type)?.label} Collection`}
                            </h1>
                            <p className="text-gray-600 mb-3 text-sm">{product.skuId || 'abc-315'}</p>
                            <p className="text-4xl font-medium text-black">
                                {product.price || '$5,200'}
                            </p>
                        </div>

                        {/* Product Description */}
                        <div>
                            <p className="text-gray-700 leading-relaxed">
                                {product.description || 'Diamond open wing ring in 18K recycled gold handcrafted with Phoenix lab grown diamonds.'}
                            </p>
                        </div>

                        {/* Color Options */}
                        <div>
                            <h3 className="text-lg font-medium mb-3">Color</h3>
                            <div className="flex space-x-4">
                                {['Yellow', 'Rose', 'White'].map((color) => (
                                    <div key={color} className="flex flex-col items-center space-y-2">
                                        <button
                                            onClick={() => setSelectedColor(color)}
                                            className={`w-10 h-10 rounded-full border-2 transition-all ${
                                                selectedColor === color
                                                    ? 'border-black scale-110 shadow-lg'
                                                    : 'border-gray-300 hover:border-gray-500'
                                            } ${
                                                color === 'Yellow' ? 'bg-yellow-400' :
                                                color === 'Rose' ? 'bg-rose-400' :
                                                'bg-gray-200'
                                            }`}
                                            title={color}
                                        />
                                        <span className={`text-xs ${selectedColor === color ? 'font-medium' : 'text-gray-600'}`}>
                                            {color}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Size Selection */}
                        <div>
                            <h3 className="text-lg font-medium mb-3">Size</h3>
                            <Select
                                value={selectedSize}
                                onChange={setSelectedSize}
                                className="w-full"
                                size="large"
                                options={[
                                    { value: '4', label: '4' },
                                    { value: '5', label: '5' },
                                    { value: '6', label: '6' },
                                    { value: '7', label: '7' },
                                    { value: '8', label: '8' },
                                    { value: '9', label: '9' },
                                    { value: '10', label: '10' },
                                ]}
                            />
                        </div>

                        {/* Material Selection */}
                        <div>
                            <h3 className="text-lg font-medium mb-3">Material</h3>
                            <Select
                                value={selectedMaterial}
                                onChange={setSelectedMaterial}
                                className="w-full"
                                size="large"
                                options={[
                                    { value: '10K', label: '10K Gold' },
                                    { value: '14K', label: '14K Gold' },
                                    { value: '18K', label: '18K Gold' },
                                    { value: 'Silver', label: 'Silver' },
                                ]}
                            />
                        </div>

                        {/* Diamond Weight */}
                        <div>
                            <h3 className="text-lg font-medium mb-3">Diamond Weight</h3>
                            <Select
                                value={diamondWeight}
                                onChange={setDiamondWeight}
                                className="w-full"
                                size="large"
                                options={[
                                    { value: '0.25 ct', label: '0.25 ct' },
                                    { value: '0.50 ct', label: '0.50 ct' },
                                    { value: '0.75 ct', label: '0.75 ct' },
                                    { value: '1.00 ct', label: '1.00 ct' },
                                ]}
                            />
                        </div>

                        {/* Jewellery Certificate */}
                        <div className="flex items-center justify-between py-4 border-t border-b border-gray-200">
                            <span className="text-lg">Jewellery Certificate:</span>
                            <div className="flex items-center space-x-2">
                                <span className="text-green-600 font-medium">Yes</span>
                                <span className="text-gray-500">✓ No</span>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="space-y-4">
                            <Button
                                type="primary"
                                size="large"
                                className="w-full bg-black hover:bg-gray-800 border-black text-white font-medium py-3 h-auto"
                                onClick={handleAddToCart}
                                icon={<PiHandbag className="w-5 h-5" />}
                            >
                                PLACE ORDER
                            </Button>

                            <div className="flex items-center space-x-4 text-sm">
                                <span className="flex items-center space-x-1">
                                    <span className="w-4 h-4 bg-black rounded-full flex items-center justify-center">
                                        <span className="text-white text-xs">7</span>
                                    </span>
                                    <span>days</span>
                                </span>
                            </div>
                        </div>

                        {/* Custom Design Link */}
                        <div className="pt-4">
                            <p className="text-gray-700">
                                Looking for Custom Design Jewellery?{' '}
                                <button
                                    onClick={handleCustomDesign}
                                    className="text-black underline hover:no-underline font-medium"
                                >
                                    Custom Design
                                </button>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Product Details and Specifications */}
            <div className="px-8 py-12 bg-gray-50">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
                    {/* Product Details */}
                    <div>
                        <h2 className="text-2xl font-medium mb-6">PRODUCT DETAILS</h2>
                        <div className="space-y-4 text-sm">
                            <p className="text-gray-700 leading-relaxed">
                                This Phoenix Diamond Wing Open Ring features an innovative design that captures the essence of flight and freedom. The ring is meticulously handcrafted using 18K recycled gold, making it both luxurious and environmentally conscious. The diamond setting is expertly crafted to maximize brilliance and fire, while the open wing design adds a modern touch to this timeless piece.
                            </p>
                            <p className="text-gray-700 leading-relaxed">
                                Each ring is individually crafted by our master jewelers, ensuring that no two pieces are exactly alike. The Phoenix lab grown diamonds are ethically sourced and certified, providing the same optical, chemical, and physical properties as mined diamonds while being more sustainable and affordable.
                            </p>
                        </div>
                    </div>

                    {/* Diamond Details */}
                    <div>
                        <h2 className="text-2xl font-medium mb-6">DIAMOND DETAILS</h2>
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                                <thead>
                                    <tr className="border-b border-gray-300">
                                        <th className="text-left py-2 font-medium">Shape</th>
                                        <th className="text-left py-2 font-medium">Weight</th>
                                        <th className="text-left py-2 font-medium">Color</th>
                                        <th className="text-left py-2 font-medium">Clarity</th>
                                        <th className="text-left py-2 font-medium">Qty</th>
                                    </tr>
                                </thead>
                                <tbody className="text-gray-700">
                                    <tr className="border-b border-gray-200">
                                        <td className="py-2">Round</td>
                                        <td className="py-2">0.5</td>
                                        <td className="py-2">G</td>
                                        <td className="py-2">VS1</td>
                                        <td className="py-2">4</td>
                                    </tr>
                                    <tr className="border-b border-gray-200">
                                        <td className="py-2">Round</td>
                                        <td className="py-2">0.5</td>
                                        <td className="py-2">G</td>
                                        <td className="py-2">VS1</td>
                                        <td className="py-2">6</td>
                                    </tr>
                                    <tr className="border-b border-gray-200">
                                        <td className="py-2">Round</td>
                                        <td className="py-2">0.5</td>
                                        <td className="py-2">G</td>
                                        <td className="py-2">VS1</td>
                                        <td className="py-2">4</td>
                                    </tr>
                                    <tr>
                                        <td className="py-2">Round</td>
                                        <td className="py-2">0.5</td>
                                        <td className="py-2">G</td>
                                        <td className="py-2">VS1</td>
                                        <td className="py-2">6</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {/* Material Details & Measurements */}
                    <div>
                        <h2 className="text-2xl font-medium mb-6">MEASUREMENTS</h2>
                        <div className="space-y-6">
                            {/* Material Details */}
                            <div>
                                <h3 className="font-medium mb-3">MATERIAL DETAILS</h3>
                                <div className="space-y-2 text-sm text-gray-700">
                                    <div className="flex justify-between">
                                        <span>Unit Cost:</span>
                                        <span>₹ 6,500 per</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Weight:</span>
                                        <span>3.5 gm</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Rate:</span>
                                        <span>₹ 6,500</span>
                                    </div>
                                </div>
                            </div>

                            {/* Measurements */}
                            <div>
                                <h3 className="font-medium mb-3">MEASUREMENTS</h3>
                                <div className="space-y-2 text-sm text-gray-700">
                                    <div className="flex justify-between">
                                        <span>L x W:</span>
                                        <span>15.5</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>H:</span>
                                        <span>2.5</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>CTW:</span>
                                        <span>0.25</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>CTW:</span>
                                        <span>0.25</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>CTW:</span>
                                        <span>0.25</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>CTW:</span>
                                        <span>0.25</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* You may also like */}
            <div className="px-8 py-12">
                <h2 className="text-2xl font-medium text-center mb-12">You may also like</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {relatedProducts.map((relatedProduct) => (
                        <div
                            key={relatedProduct.value}
                            className="group cursor-pointer"
                            onClick={() => router.push(`/jewellery/product-detail?id=${relatedProduct.value}`)}
                        >
                            <div className="relative aspect-square bg-gray-50 rounded-lg overflow-hidden mb-4">
                                <Image
                                    src={relatedProduct.image}
                                    alt={relatedProduct.type}
                                    fill
                                    className="object-contain group-hover:scale-105 transition-transform duration-300"
                                />

                                {/* Wishlist Icon */}
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        // Handle wishlist toggle for related product
                                    }}
                                    className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity bg-white/80 hover:bg-white rounded-full p-2 shadow-md"
                                >
                                    {relatedProduct.isInCart ? (
                                        <IoHeart className="w-5 h-5 text-red-500" />
                                    ) : (
                                        <IoHeartOutline className="w-5 h-5 text-gray-700" />
                                    )}
                                </button>
                            </div>

                            <div className="text-center">
                                <h3 className="text-lg font-medium text-[#946038] uppercase mb-1">
                                    {JEWELLERY_TYPE.find(t => t.value === relatedProduct.type)?.label || relatedProduct.type}
                                </h3>
                                <p className="text-gray-600 text-sm mb-1">{relatedProduct.skuId}</p>
                                <p className="text-lg font-medium">{relatedProduct.price}</p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Footer */}
            <Footer />
        </div>
    );
};

export default ProductDetail;
