import React from 'react'
import Image from "next/image";

const Footer = () => {
    return (
        <div className="flex justify-center items-center">
            <div className="relative w-full flex justify-center items-center">
                <Image
                    src="/assets/home/<USER>"
                    width={100}
                    height={100}
                    alt="Shreeji Footer"
                    className="w-full h-auto"
                />
                {/* Black Overlay with 50% Opacity */}
                <div className="absolute inset-0 bg-black opacity-50 z-10 backdrop-blur-sm" />

                <div className="absolute inset-0 z-10 flex items-start justify-between text-white text-center px-10 py-[130px]">
                    <p className="font-[500] cursor-pointer">About Us</p>
                    <p className="font-[500] cursor-pointer">B2B Login</p>
                    <div className="flex flex-col text-start">
                        <p className="font-[500] mb-2">Discover</p>
                        <p className="font-[400] text-[14px] cursor-pointer">Jewellery</p>
                        <p className="font-[400] text-[14px] cursor-pointer">Diamond</p>
                        <p className="font-[400] text-[14px] cursor-pointer">Custom Design</p>
                        <p className="font-[400] text-[14px] cursor-pointer">Catalog</p>
                    </div>
                    <div className="flex flex-col text-start">
                        <p className="font-[500] mb-2">Contact Us</p>
                        <p className="font-[400] text-[14px] cursor-pointer"><EMAIL></p>
                    </div>
                    <div className="flex flex-col text-start">
                        <p className="font-[500] mb-2">Legal</p>
                        <p className="font-[400] text-[14px] cursor-pointer">Terms & Conditions</p>
                        <p className="font-[400] text-[14px] cursor-pointer">Privacy Policy</p>
                        <p className="font-[400] text-[14px] cursor-pointer">Shipping Policy</p>
                    </div>
                    <div className="flex flex-col text-start">
                        <p className="font-[500] mb-2">Be Part On</p>
                        <div className="">
                            <Image
                                src="/assets/home/<USER>"
                                width={100}
                                height={100}
                                alt="Shreeji Footer"
                                className="w-full h-auto cursor-pointer"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Footer
