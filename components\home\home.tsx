"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { poppins } from "@/src/common/helper";
import { COLLECTION_TYPE, CATEGORY_TYPE } from "@/src/libs/constants"
import Footer from "./footer";
import AOS from 'aos';
import 'aos/dist/aos.css';

const Home = () => {
  const [selected, setSelected] = useState("RING");
  const [hovered, setHovered] = useState("OUR_LEGACY");

  useEffect(() => {
    AOS.init({
      duration: 800,
      once: false,
    });
  }, []);

  return (
    <div className={`w-auto h-auto space-y-14 select-none ${poppins.className}`}>
      {/* Section 1: Hero Section For Video */}
      <div className="flex justify-center relative">
        <video
          src="/assets/home/<USER>"
          className="object-cover w-screen h-screen"
          autoPlay
          loop
          muted
          playsInline
        >
          Your browser does not support the video tag.
        </video>

        <div className="absolute py-3 my-2 px-5 backdrop-blur-md rounded-4xl border-0">
          <Image
            src="/assets/home/<USER>"
            width={151}
            height={68}
            alt="Shreeji Gems Logo"
            data-aos="zoom-in"
          />
        </div>
      </div>

      {/* Section 2: Slogan of project */}
      <div className="relative flex justify-center items-center"
        data-aos="fade-down">
        {/* Black text on top */}
        <p className="absolute text-center text-[24px] text-black">
          It has always been to produce awesome products for the <br />
          dynamic urban lifestyle of the modern woman.
        </p>

        {/* Light gray larger text below */}
        <p className="text-center text-[40px] text-[#EAEAEA]">
          It has always been to produce awesome products for the <br />
          dynamic urban lifestyle of the modern woman.
        </p>
      </div>

      {/* Section 3: Jewellery and Diamond section */}
      <div className="flex justify-center items-center px-10 mx-auto gap-20">
        {/* Jewellery */}
        <div className="relative" data-aos="fade-down">
          <video
            src="/assets/home/<USER>"
            className="w-auto min-w-[40vw] h-[90vh] min-h-[600px] object-cover rounded-2xl shadow-2xl"
            autoPlay
            loop
            muted
            playsInline
          >
            Your browser does not support the video tag.
          </video>

          <div className="absolute inset-0 z-10 flex flex-col items-center justify-end mb-10 text-white text-center">
            <p className="font-[400] text-[40px]">Jewellerys</p>
            <p className="font-[400] text-[24px]">Where Elegance Meets Eternity</p>
          </div>
        </div>

        {/* Diamond */}
        <div className="relative" data-aos="fade-down">
          <video
            src="/assets/home/<USER>"
            className="w-auto min-w-[40vw] h-[90vh] min-h-[600px] object-cover rounded-2xl shadow-2xl"
            autoPlay
            loop
            muted
            playsInline
          >
            Your browser does not support the video tag.
          </video>

          <div className="absolute inset-0 z-10 flex flex-col items-center justify-end mb-10 text-white text-center">
            <p className="font-[400] text-[40px]">Diamonds</p>
            <p className="font-[400] text-[24px]">Where Elegance Meets Eternity</p>
          </div>
        </div>
      </div>

      {/* Section 4: Latest Collection section */}
      <div className="flex justify-center px-10 mx-xuto space-x-10 h-[50vh] min-h-[500px] bg-[#E5E5E5] my-40">
        {/* Necklace */}
        <div className="flex justify-center items-center">
          <div className="bg-white flex flex-col justify-center items-center px-6 pb-10">
            <Image
              src="/assets/home/<USER>/Necklace.svg"
              width={100}
              height={100}
              alt="Shreeji Necklace"
              className="w-[155px] h-[201px]"
              data-aos="flip-left"
            />
            <div className="py-7 text-center w-full">
              <p className="text-black text-[16px]">Shell and Pearl Necklace Set</p>
              <p className="text-[#787878] text-[14px]">18ct Gold Plated Vermeil</p></div>
          </div>
        </div>

        {/* Modal With Necklace */}
        <div className="flex justify-center items-center">
          <Image
            src="/assets/home/<USER>/Modal With Necklace.svg"
            width={100}
            height={100}
            alt="Shreeji Modal With Necklace"
            className="w-auto h-[80vh] min-h-[600px]"
            data-aos="flip-right"
          />
        </div>

        {/* Latest Collections */}
        <div className="flex flex-col justify-center">
          <p className="text-[48px] text-black font-[400]" data-aos="zoom-in">Latest Collections</p>
          <div className="">
            <div className="space-y-2 py-6" data-aos="zoom-in">
              {COLLECTION_TYPE.map((item) => (
                <div
                  key={item.value}
                  onClick={() => setSelected(item.value)}
                  className={`flex items-center cursor-pointer transition-all ${selected === item.value ? "text-[#946038] font-[500]" : "text-[#C6AA97]"}`}
                >
                  {/* Show bullet only if selected */}
                  {selected === item.value && (
                    <span className="mr-2 text-[#946038] text-xl">•</span>
                  )}
                  <span className="text-[18px]">{item.label}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Section 5: Category */}
      <div className="flex items-stretch rounded-[10px] overflow-hidden h-[90vh] min-h-[600px] shadow-lg m-3 mb-20 bg-[#E5E5E5]" data-aos="fade-up">
        {CATEGORY_TYPE.map((item, index) => {
          const isActive = hovered === item.value;
          const isLast = index === CATEGORY_TYPE.length - 1;

          return (
            <div
              key={item.value}
              onMouseEnter={() => setHovered(item.value)}
              className={`transition-[flex] duration-500 p-2 ease-in-out border-[1px] rounded-[8px] m-3 ${isActive ? "flex-[10]" : "flex-[1]"} bg-[#E5E5E5] cursor-default flex items-center justify-center relative overflow-hidden p-3`}
            >
              {/* Expanded View */}
              {isActive ? (
                <div className="flex items-center justify-center gap-6 px-6 text-center animate-fade-in">
                  <img
                    src={item.img}
                    alt={item.label}
                    className="h-[40%] transition-transform duration-700 scale-80"
                  />
                  <div>
                    <h3 className="text-[35px] font-[400] mb-3">{item.selected_text || item.label}</h3>
                    <p className="text-[14px] text-gray-700">
                      {item.description}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="transform -rotate-90 whitespace-nowrap text-[30px] font-[500] text-gray-800 tracking-tight">
                  {item.label}
                </div>
              )}
            </div>
          );
        })}
      </div>


      {/* Section 6: Footer */}
      <Footer />
    </div>
  )
}

export default Home;
