"use client";
import React from "react";
import { poppins } from "@/src/common/helper";

interface DropdownItem {
  value: string;
  label: string;
  types?: { value: string; label: string }[];
}

interface NavbarDropdownProps {
  items: DropdownItem[];
  isVisible: boolean;
}

const NavbarDropdown: React.FC<NavbarDropdownProps> = ({ items, isVisible }) => {
  if (!isVisible || !items.length) return null;

  return (
    <div className="absolute top-[158px] left-0 w-full bg-white shadow-xl border-t border-gray-100 z-50 animate-fade-in">
      <div className={`w-full px-16 py-8 ${poppins.className}`}>
        <div className="grid grid-cols-4 gap-16 max-w-6xl mx-auto">
          {items.map((category) => (
            <div key={category.value} className="space-y-6">
              <h3 className="text-[16px] font-[600] text-black uppercase tracking-wider">
                {category.label}
              </h3>
              {category.types && category.types.length > 0 ? (
                <ul className="space-y-3">
                  {category.types.map((type) => (
                    <li key={type.value}>
                      <a
                        href="#"
                        className="text-[14px] text-gray-600 hover:text-black transition-colors duration-200 block py-1 capitalize"
                      >
                        {type.label}
                      </a>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-[14px] text-gray-400 italic">
                  Coming Soon
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NavbarDropdown;
