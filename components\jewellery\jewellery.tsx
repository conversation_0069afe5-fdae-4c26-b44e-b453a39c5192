"use client";
import React, { useCallback, useState } from 'react'
import Footer from "../home/<USER>";
import Navbar from "./navbar";
import JewelleryProductList from "./product-listing";
import Loader from "../../components/common/loader"
import Image from "next/image";
import { useRouter } from "next/navigation";
import { poppins } from "@/src/common/helper";
import { IoIosArrowDown } from "react-icons/io";
import SearchBar from "../common/searchBar";
import { debounce } from "lodash";
import { Modal, Slider, Badge } from "antd";

// Sort options constant
const SORT_OPTIONS = [
    { value: "FEATURED", label: "Featured" },
    { value: "BEST_SELLING", label: "Best Selling" },
    { value: "PRICE_LOW_TO_HIGH", label: "Price: Low to High" },
    { value: "PRICE_HIGH_TO_LOW", label: "Price: High to Low" },
    { value: "DATE_NEW_TO_OLD", label: "Date: New to Old" },
    { value: "DATE_OLD_TO_NEW", label: "Date: Old to New" },
    { value: "ALPHABETICALLY_Z_TO_A", label: "Alphabetically: Z to A" },
    { value: "ALPHABETICALLY_A_TO_Z", label: "Alphabetically: A to Z" },
];

// Filter options constants
const CATEGORIES = [
    { value: "ENGAGEMENT", label: "Engagement" },
    { value: "WEDDING", label: "Wedding" },
    { value: "SOLITAIRE", label: "Solitaire" },
    { value: "BAND", label: "Band" },
    { value: "HALO", label: "Halo" },
    { value: "MULTI_STONE", label: "Multi-Stone" },
];

const MATERIALS = [
    { value: "SILVER", label: "Silver" },
    { value: "10_KT", label: "10 KT" },
    { value: "14_KT", label: "14 KT" },
    { value: "18_KT", label: "18 KT" },
];

const COLORS = [
    { value: "YELLOW", label: "Yellow" },
    { value: "ROSE", label: "Rose" },
    { value: "WHITE", label: "White" },
];

const DIAMOND_SHAPES = [
    { value: "ROUND", label: "ROUND" },
    { value: "PRINCESS", label: "PRINCESS" },
    { value: "CUSHION", label: "CUSHION" },
    { value: "ASSCHER", label: "ASSCHER" },
    { value: "OVAL", label: "OVAL" },
    { value: "EMERALD", label: "EMERALD" },
    { value: "RADIANT", label: "RADIANT" },
    { value: "MARQUISE", label: "MARQUISE" },
    { value: "HEART", label: "HEART" },
    { value: "PEAR", label: "PEAR" },
];

const SIZES = [
    { value: "3", label: "3" },
    { value: "3.5", label: "3.5" },
    { value: "4", label: "4" },
    { value: "4.5", label: "4.5" },
    { value: "5", label: "5" },
    { value: "5.5", label: "5.5" },
    { value: "6", label: "6" },
    { value: "6.5", label: "6.5" },
    { value: "7", label: "7" },
    { value: "7.5", label: "7.5" },
    { value: "8", label: "8" },
    { value: "8.5", label: "8.5" },
    { value: "9", label: "9" },
    { value: "9.5", label: "9.5" },
];

const Jewellery = () => {
    const router = useRouter();
    const [search, setSearch] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(search);

    // Modal states
    const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
    const [isSortModalOpen, setIsSortModalOpen] = useState(false);

    // Filter states
    const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
    const [priceRange, setPriceRange] = useState<[number, number]>([500, 5000]);
    const [selectedMaterials, setSelectedMaterials] = useState<string[]>([]);
    const [selectedColors, setSelectedColors] = useState<string[]>([]);
    const [selectedDiamondShapes, setSelectedDiamondShapes] = useState<string[]>([]);
    const [selectedSizes, setSelectedSizes] = useState<string[]>([]);
    const [diamondWeight, setDiamondWeight] = useState<string>("");

    // Sort state
    const [selectedSort, setSelectedSort] = useState<string>("FEATURED");

    const handleSearch = useCallback(
        debounce((query: any) => {
            setDebouncedSearchQuery(query);
            // fecthList(1, query);
        }, 500),
        []
    );

    // Check if any filters are applied
    const hasActiveFilters = () => {
        return (
            selectedCategories.length > 0 ||
            selectedMaterials.length > 0 ||
            selectedColors.length > 0 ||
            selectedDiamondShapes.length > 0 ||
            selectedSizes.length > 0 ||
            diamondWeight !== "" ||
            priceRange[0] > 500 ||
            priceRange[1] < 5000
        );
    };

    // Check if sort is applied (not default)
    const hasActiveSort = () => {
        return selectedSort !== "FEATURED";
    };

    // Reset all filters
    const resetFilters = () => {
        setSelectedCategories([]);
        setSelectedMaterials([]);
        setSelectedColors([]);
        setSelectedDiamondShapes([]);
        setSelectedSizes([]);
        setDiamondWeight("");
        setPriceRange([500, 5000]);
    };

    // Apply filters (close modal)
    const applyFilters = () => {
        setIsFilterModalOpen(false);
        // Here you would typically trigger a data fetch with the applied filters
        console.log("Applied filters:", {
            categories: selectedCategories,
            materials: selectedMaterials,
            colors: selectedColors,
            diamondShapes: selectedDiamondShapes,
            sizes: selectedSizes,
            diamondWeight,
            priceRange
        });
    };

    // Apply sort (close modal)
    const applySort = () => {
        setIsSortModalOpen(false);
        // Here you would typically trigger a data fetch with the applied sort
        console.log("Applied sort:", selectedSort);
    };

    return (
        <div className={`w-auto h-auto space-y-14 select-none ${poppins.className}`}>
            {/* Section 1: Hero Section */}
            <div className="relative w-full h-[90vh]">
                {/* Navbar on top of image */}
                <div className="absolute top-0 left-0 w-full z-10">
                    <Navbar />
                </div>

                {/* Hero Image */}
                <Image
                    src="/assets/jewellery/banner.svg"
                    alt="Shreeji Gems Logo"
                    fill
                    className="object-cover"
                    priority
                />
            </div>

            {/* Section 2: Filter, Search & Sort */}
            <div className="flex justify-between items-center gap-6 px-10">
                {/* Filter */}
                <Badge dot={hasActiveFilters()} offset={[-5, 5]}>
                    <div
                        className="flex border-2 h-[52px] border-black rounded-[50px] px-5 justify-center items-center cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => setIsFilterModalOpen(true)}
                    >
                        <Image
                            src="/assets/jewellery/filter-icon.svg"
                            alt="Filter"
                            width={24}
                            height={24}
                            className=""
                        />
                        <p className="px-2 font-[500]">Filter</p>
                        <IoIosArrowDown className="h-[20px] w-auto cursor-pointer mt-1" />
                    </div>
                </Badge>

                {/* Search */}
                <div className="flex min-w-[550px] h-[52px] border-2 border-black rounded-[50px] px-3 py-1 justify-center items-center">
                    <SearchBar {...{ search, setSearch, handleSearch }} />
                </div>

                {/* Sort */}
                <Badge dot={hasActiveSort()} offset={[-5, 5]}>
                    <div
                        className="flex border-2 h-[52px] border-black rounded-[50px] px-5 justify-center items-center cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => setIsSortModalOpen(true)}
                    >
                        <Image
                            src="/assets/jewellery/filter-icon.svg"
                            alt="Sort"
                            width={24}
                            height={24}
                            className=""
                        />
                        <p className="px-2 font-[500]">Sort</p>
                        <IoIosArrowDown className="h-[20px] w-auto cursor-pointer mt-1" />
                    </div>
                </Badge>
            </div>

            {/* Section 3: Jewellery Items */}
            <div>
                <JewelleryProductList />
            </div>

            {/* Section 4: More loader */}
            <Loader />

            {/* Section : Footer Section */}
            <Footer />

            {/* Filter Modal */}
            <Modal
                title={<div className="text-black text-[18px] font-[500] text-start">FILTER</div>}
                open={isFilterModalOpen}
                onCancel={() => setIsFilterModalOpen(false)}
                footer={null}
                width={400}
                className={`text-black text-[14px] font-[500] text-start ${poppins.className}`}
                centered
                closable={true}
                maskClosable={false}
            >
                <div className="max-h-[80vh] overflow-y-scroll scrollbar">
                    <div className="space-y-6">
                        {/* Categories */}
                        <div>
                            <h3 className="text-[14px] font-[500] text-black mb-3 border-b border-black pb-2">CATEGORIES</h3>
                            <div className="space-y-2">
                                {CATEGORIES.map((category) => (
                                    <label key={category.value} className="flex items-center text-[14px] font-[500]">
                                        <input
                                            type="checkbox"
                                            className="mr-3 w-4 h-4"
                                            checked={selectedCategories.includes(category.value)}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    setSelectedCategories([...selectedCategories, category.value]);
                                                } else {
                                                    setSelectedCategories(selectedCategories.filter(c => c !== category.value));
                                                }
                                            }}
                                        />
                                        {category.label}
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Price */}
                        <div>
                            <h3 className="text-[14px] font-[500] text-black mb-3 border-b border-black pb-2">PRICE</h3>
                            <div className="px-2">
                                <Slider
                                    range
                                    min={500}
                                    max={5000}
                                    step={100}
                                    value={priceRange}
                                    onChange={(value) => setPriceRange(value as [number, number])}
                                    tooltip={{
                                        formatter: (value) => `₹${value}`
                                    }}
                                />
                                <div className="flex justify-between text-[14px] font-[400] text-black mt-2">
                                    <span>₹{priceRange[0]}</span>
                                    <span>₹{priceRange[1]}</span>
                                </div>
                            </div>
                        </div>

                        {/* Material */}
                        <div>
                            <h3 className="text-[14px] font-[500] text-black mb-3 border-b border-black pb-2">MATERIAL</h3>
                            <div className="space-y-2">
                                {MATERIALS.map((material) => (
                                    <label key={material.value} className="flex items-center text-[14px]">
                                        <input
                                            type="checkbox"
                                            className="mr-3 w-4 h-4"
                                            checked={selectedMaterials.includes(material.value)}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    setSelectedMaterials([...selectedMaterials, material.value]);
                                                } else {
                                                    setSelectedMaterials(selectedMaterials.filter(m => m !== material.value));
                                                }
                                            }}
                                        />
                                        {material.label}
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Color */}
                        <div>
                            <h3 className="text-[14px] font-[500] text-black mb-3 border-b border-black pb-2">COLOR</h3>
                            <div className="space-y-2">
                                {COLORS.map((color) => (
                                    <label key={color.value} className="flex items-center text-[14px]">
                                        <input
                                            type="checkbox"
                                            className="mr-3 w-4 h-4"
                                            checked={selectedColors.includes(color.value)}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    setSelectedColors([...selectedColors, color.value]);
                                                } else {
                                                    setSelectedColors(selectedColors.filter(c => c !== color.value));
                                                }
                                            }}
                                        />
                                        {color.label}
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Diamond Shapes */}
                        <div>
                            <h3 className="text-[14px] font-[500] text-black mb-3 border-b border-black pb-2">DIAMOND SHAPES</h3>
                            <div className="space-y-2">
                                {DIAMOND_SHAPES.map((shape) => (
                                    <label key={shape.value} className="flex items-center text-[14px]">
                                        <input
                                            type="checkbox"
                                            className="mr-3 w-4 h-4"
                                            checked={selectedDiamondShapes.includes(shape.value)}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    setSelectedDiamondShapes([...selectedDiamondShapes, shape.value]);
                                                } else {
                                                    setSelectedDiamondShapes(selectedDiamondShapes.filter(s => s !== shape.value));
                                                }
                                            }}
                                        />
                                        {shape.label}
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Size */}
                        <div>
                            <h3 className="text-[14px] font-[500] text-black mb-3 border-b border-black pb-2">SIZE</h3>
                            <div className="grid grid-cols-2 gap-2">
                                {SIZES.map((size) => (
                                    <label key={size.value} className="flex items-center text-[14px]">
                                        <input
                                            type="checkbox"
                                            className="mr-2 w-4 h-4"
                                            checked={selectedSizes.includes(size.value)}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    setSelectedSizes([...selectedSizes, size.value]);
                                                } else {
                                                    setSelectedSizes(selectedSizes.filter(s => s !== size.value));
                                                }
                                            }}
                                        />
                                        {size.label}
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Diamond Weight */}
                        <div>
                            <h3 className="text-[14px] font-[500] text-black mb-3 border-b border-black pb-2">Diamond Weight</h3>
                            <input
                                type="text"
                                placeholder="Enter weight"
                                className="w-full p-2 border border-gray-300 rounded text-[14px]"
                                value={diamondWeight}
                                onChange={(e) => setDiamondWeight(e.target.value)}
                            />
                        </div>

                        {/* Filter Button */}
                        <div className="pt-4">
                            <button
                                onClick={applyFilters}
                                className="w-full py-3 bg-black text-white rounded-full text-[14px] font-medium hover:bg-gray-800 transition-colors"
                            >
                                Filter
                            </button>
                        </div>
                    </div>
                </div>
            </Modal>

            {/* Sort Modal */}
            <Modal
                title={<div className="text-black text-[18px] font-[500] text-start">SORT</div>}
                open={isSortModalOpen}
                onCancel={() => setIsSortModalOpen(false)}
                footer={null}
                width={400}
                className={`text-black text-[14px] font-[500] text-start ${poppins.className}`}
                centered
                closable={true}
                maskClosable={false}
            >
                <div className="max-h-[80vh] overflow-y-scroll scrollbar">
                    {/* Sort By Button */}
                    <div className="mb-6">
                        <button className="w-full py-3 px-4 border border-gray-300 rounded-full text-left flex justify-between items-center text-[14px]">
                            <span className="text-gray-500">Sort By</span>
                            <span className="text-black font-medium">{SORT_OPTIONS.find(opt => opt.value === selectedSort)?.label}</span>
                        </button>
                    </div>

                    {/* Sort Options */}
                    <div className="space-y-2">
                        {SORT_OPTIONS.slice(1).map((option) => (
                            <button
                                key={option.value}
                                onClick={() => {
                                    setSelectedSort(option.value);
                                    applySort();
                                }}
                                className={`block w-full text-left text-gray-600 hover:text-black transition-colors ${selectedSort === option.value ? 'text-black font-medium' : ''
                                    }`}
                            >
                                {option.label}
                            </button>
                        ))}
                    </div>
                </div>
            </Modal>
        </div>
    )
}

export default Jewellery
