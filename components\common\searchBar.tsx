import React from "react";
import { LuSearch } from "react-icons/lu";
import { poppins } from "@/src/common/helper";

const SearchBar = ({ search, setSearch, handleSearch }: any) => {
  return (
    <div className="flex items-center relative w-full">
      <LuSearch className="absolute left-3 top-1/2 -translate-y-1/2 w-[20px] h-[20px] text-black" />
      <input
        type="text"
        placeholder="Search for Gold Jewellery, Diamond Jewellery and more…"
        className={`pl-10 pr-4 py-2 w-full h-auto rounded-[10px] bg-white border-none text-black placeholder:text-gray-500 focus:outline-none ${poppins.className}`}
        value={search}
        onChange={(e) => {
          setSearch(e.target.value);
          handleSearch(e.target.value);
        }}
      />
    </div>
  );
};

export default SearchBar;
